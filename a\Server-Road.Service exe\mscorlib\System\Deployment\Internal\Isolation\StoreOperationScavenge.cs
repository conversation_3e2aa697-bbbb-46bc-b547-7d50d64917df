﻿using System;
using System.Runtime.InteropServices;

namespace System.Deployment.Internal.Isolation
{
	// Token: 0x020006AA RID: 1706
	internal struct StoreOperationScavenge
	{
		// Token: 0x06004FE8 RID: 20456 RVA: 0x0011CD78 File Offset: 0x0011AF78
		public StoreOperationScavenge(bool Light, ulong SizeLimit, ulong RunLimit, uint ComponentLimit)
		{
			this.Size = (uint)Marshal.SizeOf(typeof(StoreOperationScavenge));
			this.Flags = StoreOperationScavenge.OpFlags.Nothing;
			if (Light)
			{
				this.Flags |= StoreOperationScavenge.OpFlags.Light;
			}
			this.SizeReclaimationLimit = SizeLimit;
			if (SizeLimit != 0UL)
			{
				this.Flags |= StoreOperationScavenge.OpFlags.LimitSize;
			}
			this.RuntimeLimit = RunLimit;
			if (RunLimit != 0UL)
			{
				this.Flags |= StoreOperationScavenge.OpFlags.LimitTime;
			}
			this.ComponentCountLimit = ComponentLimit;
			if (ComponentLimit != 0U)
			{
				this.Flags |= StoreOperationScavenge.OpFlags.LimitCount;
			}
		}

		// Token: 0x06004FE9 RID: 20457 RVA: 0x0011CDFC File Offset: 0x0011AFFC
		public StoreOperationScavenge(bool Light)
		{
			this = new StoreOperationScavenge(Light, 0UL, 0UL, 0U);
		}

		// Token: 0x06004FEA RID: 20458 RVA: 0x0011CE0A File Offset: 0x0011B00A
		public void Destroy()
		{
		}

		// Token: 0x04002255 RID: 8789
		[MarshalAs(UnmanagedType.U4)]
		public uint Size;

		// Token: 0x04002256 RID: 8790
		[MarshalAs(UnmanagedType.U4)]
		public StoreOperationScavenge.OpFlags Flags;

		// Token: 0x04002257 RID: 8791
		[MarshalAs(UnmanagedType.U8)]
		public ulong SizeReclaimationLimit;

		// Token: 0x04002258 RID: 8792
		[MarshalAs(UnmanagedType.U8)]
		public ulong RuntimeLimit;

		// Token: 0x04002259 RID: 8793
		[MarshalAs(UnmanagedType.U4)]
		public uint ComponentCountLimit;

		// Token: 0x02000C57 RID: 3159
		[Flags]
		public enum OpFlags
		{
			// Token: 0x0400379F RID: 14239
			Nothing = 0,
			// Token: 0x040037A0 RID: 14240
			Light = 1,
			// Token: 0x040037A1 RID: 14241
			LimitSize = 2,
			// Token: 0x040037A2 RID: 14242
			LimitTime = 4,
			// Token: 0x040037A3 RID: 14243
			LimitCount = 8
		}
	}
}
